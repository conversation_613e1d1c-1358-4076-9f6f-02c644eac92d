import 'package:http/http.dart' as http;
import 'dart:io';
import 'dart:convert';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../config/app_config.dart';

class ApiService {
  // Use the centralized AppConfig for API URL
  static String get baseUrl => AppConfig.apiBaseUrl;

  // JWT token to be fetched dynamically
  static String? _jwtToken;

  // Get stored JWT token
  static String? get jwtToken => _jwtToken;

  // Default HTTP headers with dynamic JWT authentication
  static Map<String, String> get defaultHeaders {
    final Map<String, String> headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add Authorization header if token exists
    if (_jwtToken != null) {
      headers['Authorization'] = 'Bearer $_jwtToken';
    }

    return headers;
  }

  // Login to get JWT token
  Future<bool> login({String email = '<EMAIL>', String password = '123456'}) async {
    final client = createClient();
    try {
      final response = await client.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'email': email,
          'password': password,
        }),
      ).timeout(Duration(seconds: AppConfig.connectionTimeoutSeconds));

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        // Assuming the token is in response as 'access_token' or 'token'
        if (data.containsKey('access_token')) {
          _jwtToken = data['access_token'];
          return true;
        } else if (data.containsKey('token')) {
          _jwtToken = data['token'];
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Login error: $e');
      return false;
    } finally {
      client.close();
    }
  }

  // Check if the API server is available
  Future<bool> isConnectedToNetwork() async {
    try {
      // For local development, bypass network check
      // This ensures the app works with local API servers
      return true;
    } catch (e) {
      return false;
    }
  }

  // Create an HTTP client with a longer timeout
  http.Client createClient() {
    return http.Client();
  }

  // Helper method to implement retry logic with exponential backoff
  Future<http.Response> _getWithRetry(String url) async {
    final client = createClient();
    int attempts = 0;
    int backoffDelay = AppConfig.retryDelaySeconds;
    late http.Response response;

    try {
      while (attempts < AppConfig.maxRetryAttempts) {
        try {
          response = await client
              .get(Uri.parse(url), headers: defaultHeaders)
              .timeout(Duration(seconds: AppConfig.connectionTimeoutSeconds));

          if (response.statusCode == 200) {
            return response;
          } else if (response.statusCode == 401 && attempts == 0) {
            // If first attempt gets 401, try to login and get new token
            final bool loginSuccess = await login();
            if (loginSuccess) {
              // Continue to next iteration with new token
              attempts++;
              continue;
            }
          }

          // If response code is not 200, try again with exponential backoff
          attempts++;
          if (attempts < AppConfig.maxRetryAttempts) {
            // Exponential backoff: increase delay each time
            await Future.delayed(Duration(seconds: backoffDelay));
            backoffDelay *= 2; // Double the delay for next retry
          }
        } catch (e) {
          attempts++;
          if (attempts >= AppConfig.maxRetryAttempts) {
            rethrow; // Rethrow the last exception if all attempts failed
          }
          // Exponential backoff
          await Future.delayed(Duration(seconds: backoffDelay));
          backoffDelay *= 2; // Double the delay for next retry
        }
      }
      return response; // This will execute if we've exhausted retries
    } finally {
      client.close(); // Ensure client is closed only once, after all retries
    }
  }

  // POST request with API key
  Future<http.Response> _post(String url, Map<String, dynamic> body) async {
    final client = createClient();
    try {
      final response = await client
          .post(
            Uri.parse(url),
            headers: defaultHeaders,
            body: json.encode(body),
          )
          .timeout(Duration(seconds: AppConfig.connectionTimeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  // PUT request with API key
  Future<http.Response> _put(String url, Map<String, dynamic> body) async {
    final client = createClient();
    try {
      final response = await client
          .put(Uri.parse(url), headers: defaultHeaders, body: json.encode(body))
          .timeout(Duration(seconds: AppConfig.connectionTimeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  // DELETE request with API key
  Future<http.Response> _delete(String url) async {
    final client = createClient();
    try {
      final response = await client
          .delete(Uri.parse(url), headers: defaultHeaders)
          .timeout(Duration(seconds: AppConfig.connectionTimeoutSeconds));
      return response;
    } finally {
      client.close();
    }
  }

  // Fetch all products
  Future<List<ProductModel>> getProducts() async {
    try {
      bool isConnected = await isConnectedToNetwork();
      if (!isConnected) {
        throw Exception('No network connection available');
      }

      // If no token yet, try to login first
      if (_jwtToken == null) {
        await login();
      }

      final response = await _getWithRetry('$baseUrl/products');

      if (response.statusCode == 200) {
        // Parse the JSON response
        final dynamic jsonData = json.decode(response.body);
        List<dynamic> productsJson;

        // Handle specific response format with "list" key
        if (jsonData is Map<String, dynamic>) {
          if (jsonData.containsKey('list') && jsonData['list'] is List) {
            productsJson = jsonData['list'];
          } else if (jsonData.containsKey('data') && jsonData['data'] is List) {
            productsJson = jsonData['data'];
          } else {
            // Log the response for debugging
            print('Unexpected response format: ${response.body}');
            throw Exception('Unexpected response format');
          }
        } else if (jsonData is List) {
          // Handle case where response is directly a list
          productsJson = jsonData;
        } else {
          // Unexpected response format
          print('Unexpected response format: ${response.body}');
          throw Exception('Unexpected response format');
        }

        return productsJson.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception(
          'Failed to load products. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to load products: $e');
    }
  }

  // Get user profile
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      bool isConnected = await isConnectedToNetwork();
      if (!isConnected) {
        throw Exception('No network connection available');
      }

      // If no token yet, try to login first
      if (_jwtToken == null) {
        await login();
      }

      final response = await _getWithRetry('$baseUrl/user/profile');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = json.decode(response.body);
        return jsonData;
      } else {
        throw Exception(
          'Failed to load user profile. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to load user profile: $e');
    }
  }

  // Fetch products by category
  Future<List<ProductModel>> getProductsByCategory(int categoryId) async {
    try {
      bool isConnected = await isConnectedToNetwork();
      if (!isConnected) {
        throw Exception('No network connection available');
      }

      // If no token yet, try to login first
      if (_jwtToken == null) {
        await login();
      }

      // Use the correct endpoint format for fetching products by category
      final response = await _getWithRetry('$baseUrl/products?categories_id=$categoryId');

      if (response.statusCode == 200) {
        // Parse the JSON response
        final dynamic jsonData = json.decode(response.body);
        List<dynamic> productsJson;

        // Handle specific response format with "list" key
        if (jsonData is Map<String, dynamic>) {
          if (jsonData.containsKey('list') && jsonData['list'] is List) {
            productsJson = jsonData['list'];
          } else if (jsonData.containsKey('data') && jsonData['data'] is List) {
            productsJson = jsonData['data'];
          } else {
            // Log the response for debugging
            print('Unexpected response format: ${response.body}');
            throw Exception('Unexpected response format');
          }
        } else if (jsonData is List) {
          // Handle case where response is directly a list
          productsJson = jsonData;
        } else {
          // Unexpected response format
          print('Unexpected response format: ${response.body}');
          throw Exception('Unexpected response format');
        }

        return productsJson.map((json) => ProductModel.fromJson(json)).toList();
      } else {
        throw Exception(
          'Failed to load products by category. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to load products by category: $e');
    }
  }

  // Upload profile image
  Future<bool> uploadProfileImage(int userId, File imageFile) async {
    try {
      bool isConnected = await isConnectedToNetwork();
      if (!isConnected) {
        throw Exception('No network connection available');
      }

      // If no token yet, try to login first
      if (_jwtToken == null) {
        await login();
      }

      // Create multipart request for profile image upload
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/users/$userId/profile/image'),
      );

      // Add authorization header
      request.headers.addAll({
        'Authorization': 'Bearer $_jwtToken',
        'Accept': 'application/json',
      });

      // Add file to upload
      request.files.add(
        await http.MultipartFile.fromPath(
          'image',
          imageFile.path,
        ),
      );

      // Send the request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        throw Exception(
          'Failed to upload profile image. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to upload profile image: $e');
    }
  }

  // Fetch all categories
  Future<List<CategoryModel>> getCategories() async {
    try {
      bool isConnected = await isConnectedToNetwork();
      if (!isConnected) {
        throw Exception('No network connection available');
      }

      // If no token yet, try to login first
      if (_jwtToken == null) {
        await login();
      }

      final response = await _getWithRetry('$baseUrl/categories');

      if (response.statusCode == 200) {
        // Parse the JSON response
        final dynamic jsonData = json.decode(response.body);
        List<dynamic> categoriesJson;

        // Handle specific response format with "list" key
        if (jsonData is Map<String, dynamic>) {
          if (jsonData.containsKey('list') && jsonData['list'] is List) {
            categoriesJson = jsonData['list'];
          } else if (jsonData.containsKey('data') && jsonData['data'] is List) {
            categoriesJson = jsonData['data'];
          } else {
            throw Exception('Unexpected response format');
          }
        } else if (jsonData is List) {
          // Handle case where response is directly a list
          categoriesJson = jsonData;
        } else {
          // Unexpected response format
          throw Exception('Unexpected response format');
        }

        return categoriesJson.map((json) => CategoryModel.fromJson(json)).toList();
      } else {
        throw Exception(
          'Failed to load categories. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      throw Exception('Failed to load categories: $e');
    }
  }
}